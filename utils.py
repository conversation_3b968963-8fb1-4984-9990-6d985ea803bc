# 驱动工具类
import time

from selenium import webdriver


class DriverUtils:
    # 私有属性
    __driver = None
    # 获取浏览器驱动对象
    @classmethod
    def get_driver(cls):
        if cls.__driver is None:
            cls.driver=webdriver.Chrome()
            cls.driver.maximize_window()
            cls.driver.implicitly_wait(10)
        return cls.driver

    @classmethod
    # 关闭浏览器驱动对象
    def quit_driver(cls):
        # 为了增强代码的健壮性，避免单独调用关闭浏览器驱动方法时会报错，在调用关闭驱动对象的方法时先判断当前是否有打开的浏览器
        if cls.__driver is not None:
            time.sleep(3)
            cls.__driver.quit()
            cls.__driver = None

DriverUtils.get_driver()
DriverUtils.quit_driver()
DriverUtils.get_driver()