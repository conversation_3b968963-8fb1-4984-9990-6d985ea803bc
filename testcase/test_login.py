import pytest
from time import sleep

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from utils import DriverUtils


class TestLogin:

    def test_login_account_not_exist(self):
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service)
        driver.maximize_window()  # 先最大化窗口
        driver.get("http://hmshop-test.itheima.net/")
        sleep(3)
        driver.quit()
